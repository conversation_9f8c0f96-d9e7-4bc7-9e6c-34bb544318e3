"use client";

import React, { Component, ErrorInfo, ReactNode } from 'react';
import { AlertTriangle, RefreshCw } from 'lucide-react';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
}

interface State {
  hasError: boolean;
  error?: Error;
}

export class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('ErrorBoundary caught an error:', error, errorInfo);
    
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }
  }

  handleRetry = () => {
    this.setState({ hasError: false, error: undefined });
  };

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback;
      }

      return (
        <div className="flex flex-col items-center justify-center p-8 bg-white/50 dark:bg-dark-surface/50 backdrop-blur-sm rounded-2xl border border-deep-charcoal/15 dark:border-dark-text/15 shadow-lg">
          <div className="w-16 h-16 bg-red-100 dark:bg-red-900/20 rounded-full flex items-center justify-center mb-4">
            <AlertTriangle className="w-8 h-8 text-red-600 dark:text-red-400" />
          </div>
          
          <h3 className="text-lg font-semibold text-deep-charcoal dark:text-dark-text mb-2">
            Something went wrong
          </h3>
          
          <p className="text-sm text-deep-charcoal/70 dark:text-dark-text/70 text-center mb-4 max-w-md">
            We encountered an error while loading this section. Please try refreshing or contact support if the problem persists.
          </p>
          
          {process.env.NODE_ENV === 'development' && this.state.error && (
            <details className="mb-4 p-3 bg-red-50 dark:bg-red-900/10 rounded-lg border border-red-200 dark:border-red-800 max-w-md">
              <summary className="text-sm font-medium text-red-800 dark:text-red-400 cursor-pointer">
                Error Details (Development)
              </summary>
              <pre className="mt-2 text-xs text-red-700 dark:text-red-300 whitespace-pre-wrap">
                {this.state.error.message}
                {'\n'}
                {this.state.error.stack}
              </pre>
            </details>
          )}
          
          <button
            onClick={this.handleRetry}
            className="flex items-center gap-2 px-4 py-2 bg-accent-green hover:bg-accent-green/90 text-white rounded-lg transition-colors duration-200 font-medium"
          >
            <RefreshCw className="w-4 h-4" />
            Try Again
          </button>
        </div>
      );
    }

    return this.props.children;
  }
}

// Functional wrapper for easier use
interface SectionErrorBoundaryProps {
  children: ReactNode;
  sectionName?: string;
}

export function SectionErrorBoundary({ children, sectionName = "section" }: SectionErrorBoundaryProps) {
  return (
    <ErrorBoundary
      onError={(error, errorInfo) => {
        console.error(`Error in ${sectionName}:`, error, errorInfo);
        // You could send this to an error reporting service here
      }}
      fallback={
        <div className="flex flex-col items-center justify-center p-12 bg-white/30 dark:bg-dark-surface/30 backdrop-blur-sm rounded-2xl border border-deep-charcoal/10 dark:border-dark-text/10">
          <div className="w-12 h-12 bg-accent-green/10 rounded-full flex items-center justify-center mb-4">
            <AlertTriangle className="w-6 h-6 text-accent-green" />
          </div>
          <h3 className="text-lg font-semibold text-deep-charcoal dark:text-dark-text mb-2">
            Unable to load {sectionName}
          </h3>
          <p className="text-sm text-deep-charcoal/70 dark:text-dark-text/70 text-center">
            This section is temporarily unavailable. Please refresh the page.
          </p>
        </div>
      }
    >
      {children}
    </ErrorBoundary>
  );
}

// Lightweight error boundary for smaller components
export function ComponentErrorBoundary({ children }: { children: ReactNode }) {
  return (
    <ErrorBoundary
      fallback={
        <div className="flex items-center justify-center p-4 bg-red-50 dark:bg-red-900/10 rounded-lg border border-red-200 dark:border-red-800">
          <AlertTriangle className="w-4 h-4 text-red-600 dark:text-red-400 mr-2" />
          <span className="text-sm text-red-800 dark:text-red-400">
            Component failed to load
          </span>
        </div>
      }
    >
      {children}
    </ErrorBoundary>
  );
}
