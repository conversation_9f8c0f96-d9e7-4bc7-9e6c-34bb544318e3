"use client";


import { ThemeToggle } from "@/components/theme-toggle";
import { Footer } from "@/components/footer";
import Image from "next/image";
import { ExternalLink } from "lucide-react";
import Link from "next/link";

import { SmoothScrollProvider } from "@/components/animations";
import {
  HeroSection,
  AboutSection,
  ProjectSection,
  WorkExperienceSection,
  HireMeSection,
  ContributionSection
} from "@/components/sections";
import MobileBottomNav from "@/components/MobileBottomNav";



export default function Home() {
  const scrollToSection = (sectionId: string) => {
    if (typeof window !== 'undefined') {
      document.getElementById(sectionId)?.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <SmoothScrollProvider>
      <div className="min-h-screen bg-light-almond dark:bg-dark-bg transition-colors">


      {/* Navigation */}
      <nav className="flex items-center justify-between px-6 py-6 lg:px-12 max-w-7xl mx-auto relative z-50">
        {/* Logo */}
        <Link href="/" className="flex items-center space-x-3 text-deep-charcoal dark:text-dark-text hover:opacity-80 transition-opacity">
          <div className="w-10 h-10 relative ">
            <Image
              src="/hero.png"
              alt="Arkit_k Logo"
              fill
              className="object-contain rounded-full"
            />
          </div>
          <span className="text-xl font-bold">Arkit_k</span>
        </Link>

        {/* Desktop Navigation Links */}
        <div className="hidden md:flex items-center space-x-8 text-deep-charcoal dark:text-dark-text">
          <button
            onClick={() => scrollToSection('about')}
            className="hover:text-accent-green transition-colors"
          >
            About
          </button>
          <button
            onClick={() => scrollToSection('work-experience')}
            className="hover:text-accent-green transition-colors"
          >
            Experience
          </button>
          <button
            onClick={() => scrollToSection('project')}
            className="hover:text-accent-green transition-colors"
          >
            Projects
          </button>
          <button
            onClick={() => scrollToSection('hire')}
            className="hover:text-accent-green transition-colors"
          >
            Hire Me
          </button>
          <button
            onClick={() => scrollToSection('contributions')}
            className="hover:text-accent-green transition-colors"
          >
            Contributions
          </button>
        </div>

        {/* Mobile Menu Button & Theme Toggle */}
        <div className="flex items-center gap-4">
          <ThemeToggle />
          <Link
            href="https://cal.com/arkit-karmokar-x0uyir/secret"
            className="p-2 bg-deep-charcoal/10 dark:bg-dark-text/10 rounded-lg hover:bg-deep-charcoal/20 dark:hover:bg-dark-text/20 transition-colors"
            target="_blank"
            rel="noopener noreferrer"
            title="Schedule a meeting"
          >
            <ExternalLink className="w-4 h-4 text-deep-charcoal dark:text-dark-text" />
          </Link>

        </div>


      </nav>



      {/* Main Content */}
      <main className="overflow-x-hidden pb-20 md:pb-0">
        <HeroSection />
        <AboutSection />
        <ProjectSection />
        <WorkExperienceSection />
        <HireMeSection />
        <ContributionSection />
      </main>

      {/* Mobile Bottom Navigation */}
      <MobileBottomNav scrollToSection={scrollToSection} />

      {/* Footer */}
      <Footer />
      </div>
    </SmoothScrollProvider>
  );
}
