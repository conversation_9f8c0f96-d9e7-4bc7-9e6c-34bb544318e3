import React from 'react';

interface LoadingSkeletonProps {
  className?: string;
  variant?: 'card' | 'text' | 'circle' | 'rectangle';
  width?: string;
  height?: string;
  count?: number;
}

export function LoadingSkeleton({ 
  className = '', 
  variant = 'rectangle',
  width = 'w-full',
  height = 'h-4',
  count = 1
}: LoadingSkeletonProps) {
  const baseClasses = "animate-pulse bg-gradient-to-r from-deep-charcoal/10 via-deep-charcoal/5 to-deep-charcoal/10 dark:from-dark-text/10 dark:via-dark-text/5 dark:to-dark-text/10";
  
  const variantClasses = {
    card: "rounded-2xl",
    text: "rounded-md",
    circle: "rounded-full",
    rectangle: "rounded-lg"
  };

  const skeletonElement = (
    <div 
      className={`${baseClasses} ${variantClasses[variant]} ${width} ${height} ${className}`}
    />
  );

  if (count === 1) {
    return skeletonElement;
  }

  return (
    <>
      {Array.from({ length: count }).map((_, index) => (
        <div key={index} className="mb-2">
          {skeletonElement}
        </div>
      ))}
    </>
  );
}

export function WorkExperienceCardSkeleton() {
  return (
    <div className="bg-white/70 dark:bg-dark-surface/70 backdrop-blur-sm rounded-2xl p-5 border border-deep-charcoal/15 dark:border-dark-text/15 shadow-lg">
      {/* Header */}
      <div className="flex items-start gap-4 mb-4">
        <LoadingSkeleton variant="circle" width="w-12" height="h-12" />
        <div className="flex-1 min-w-0">
          <div className="flex items-start justify-between gap-2 mb-2">
            <LoadingSkeleton variant="text" width="w-3/4" height="h-5" />
            <LoadingSkeleton variant="text" width="w-16" height="h-6" className="rounded-full" />
          </div>
          <LoadingSkeleton variant="text" width="w-1/2" height="h-4" />
        </div>
      </div>

      {/* Timeline */}
      <div className="flex items-center gap-6 mb-4">
        <LoadingSkeleton variant="text" width="w-20" height="h-6" className="rounded-lg" />
        <LoadingSkeleton variant="text" width="w-24" height="h-6" className="rounded-lg" />
      </div>

      {/* Description */}
      <div className="mb-4 space-y-2">
        <LoadingSkeleton variant="text" width="w-full" height="h-4" />
        <LoadingSkeleton variant="text" width="w-5/6" height="h-4" />
        <LoadingSkeleton variant="text" width="w-3/4" height="h-4" />
      </div>

      {/* Technologies */}
      <div>
        <LoadingSkeleton variant="text" width="w-20" height="h-3" className="mb-2" />
        <div className="flex flex-wrap gap-2">
          {Array.from({ length: 4 }).map((_, index) => (
            <LoadingSkeleton 
              key={index}
              variant="text" 
              width="w-16" 
              height="h-6" 
              className="rounded-lg" 
            />
          ))}
          <LoadingSkeleton variant="text" width="w-12" height="h-6" className="rounded-lg" />
        </div>
      </div>
    </div>
  );
}

export function WorkExperienceSectionSkeleton() {
  return (
    <section id="work-experience" className="px-6 lg:px-12 mt-24">
      <div className="max-w-7xl mx-auto">
        <div className="mb-16">
          <LoadingSkeleton variant="text" width="w-64" height="h-10" className="mb-4" />
          <LoadingSkeleton variant="text" width="w-96" height="h-6" />
        </div>

        {/* Mobile Card Layout Skeleton */}
        <div className="md:hidden space-y-6">
          {Array.from({ length: 3 }).map((_, index) => (
            <WorkExperienceCardSkeleton key={index} />
          ))}
        </div>

        {/* Desktop Layout Skeleton */}
        <div className="hidden md:block space-y-8">
          {Array.from({ length: 3 }).map((_, index) => (
            <div key={index} className="flex gap-6">
              <LoadingSkeleton variant="circle" width="w-12" height="h-12" />
              <div className="flex-1">
                <div className="bg-white/70 dark:bg-dark-surface/70 backdrop-blur-sm rounded-2xl p-6 border border-deep-charcoal/15 dark:border-dark-text/15 shadow-lg">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex-1">
                      <LoadingSkeleton variant="text" width="w-3/4" height="h-6" className="mb-2" />
                      <LoadingSkeleton variant="text" width="w-1/2" height="h-5" />
                    </div>
                    <LoadingSkeleton variant="text" width="w-20" height="h-6" className="rounded-full" />
                  </div>
                  <div className="space-y-2 mb-4">
                    <LoadingSkeleton variant="text" width="w-full" height="h-4" />
                    <LoadingSkeleton variant="text" width="w-5/6" height="h-4" />
                  </div>
                  <div className="flex flex-wrap gap-2">
                    {Array.from({ length: 6 }).map((_, techIndex) => (
                      <LoadingSkeleton 
                        key={techIndex}
                        variant="text" 
                        width="w-16" 
                        height="h-6" 
                        className="rounded-lg" 
                      />
                    ))}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
